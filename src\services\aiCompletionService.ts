import { Logger } from '../utils/logger';

/**
 * AI 补全请求配置
 */
export interface AICompletionConfig {
    /** 最大提示词长度 */
    maxPromptLength: number;
    /** 请求超时时间（毫秒） */
    timeout: number;
    /** 模拟延迟时间（毫秒） */
    simulatedDelay: number;
}

/**
 * AI 补全响应
 */
export interface AICompletionResponse {
    /** 补全文本 */
    completion: string;
    /** 是否成功 */
    success: boolean;
    /** 错误信息（如果有） */
    error?: string;
}

/**
 * AI 补全服务
 * 负责处理与 AI API 的交互
 */
export class AICompletionService {
    private config: AICompletionConfig;

    constructor(config?: Partial<AICompletionConfig>) {
        this.config = {
            maxPromptLength: 1000,
            timeout: 5000,
            simulatedDelay: 50,
            ...config
        };
        
        Logger.info('AICompletionService 初始化完成', this.config);
    }

    /**
     * 获取 AI 补全
     * @param prompt 提示词
     * @returns AI 补全响应
     */
    async getCompletion(prompt: string): Promise<AICompletionResponse> {
        Logger.methodCall('AICompletionService', 'getCompletion');
        
        try {
            // 预处理提示词
            const processedPrompt = this.preprocessPrompt(prompt);
            Logger.debug(`处理后的提示词长度: ${processedPrompt.length}`);
            
            // 模拟 AI API 调用
            const completion = await this.callAIAPI(processedPrompt);
            
            Logger.debug(`AI 返回的补全: ${completion}`);
            
            return {
                completion,
                success: true
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            Logger.error('AI 补全请求失败', error as Error);
            
            return {
                completion: '',
                success: false,
                error: errorMessage
            };
        }
    }

    /**
     * 预处理提示词
     * @param prompt 原始提示词
     * @returns 处理后的提示词
     */
    private preprocessPrompt(prompt: string): string {
        // 限制提示词长度
        if (prompt.length > this.config.maxPromptLength) {
            const truncated = prompt.substring(prompt.length - this.config.maxPromptLength);
            Logger.debug(`提示词被截断，原长度: ${prompt.length}, 新长度: ${truncated.length}`);
            return truncated;
        }
        
        return prompt;
    }

    /**
     * 模拟调用 AI API
     * 在真实应用中，这里将是调用 OpenAI、Gemini 等 API 的网络请求
     * @param prompt 提示词
     * @returns 补全文本
     */
    private async callAIAPI(prompt: string): Promise<string> {
        Logger.debug(`向 AI 发送提示词: ${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}`);
        
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, this.config.simulatedDelay));
        
        // 模拟不同的补全结果
        const completions = [
            'console.log("Hello from AI!");',
            'const result = await fetch("/api/data");',
            'if (condition) {\n    return true;\n}',
            '// TODO: 实现这个功能',
            'try {\n    // 代码逻辑\n} catch (error) {\n    console.error(error);\n}'
        ];
        
        // 根据提示词内容选择合适的补全
        const completion = this.selectCompletion(prompt, completions);
        
        return completion;
    }

    /**
     * 根据提示词选择合适的补全
     * @param prompt 提示词
     * @param completions 可选的补全列表
     * @returns 选中的补全
     */
    private selectCompletion(prompt: string, completions: string[]): string {
        const lowerPrompt = prompt.toLowerCase();
        
        if (lowerPrompt.includes('console') || lowerPrompt.includes('log')) {
            return completions[0];
        } else if (lowerPrompt.includes('fetch') || lowerPrompt.includes('api')) {
            return completions[1];
        } else if (lowerPrompt.includes('if') || lowerPrompt.includes('condition')) {
            return completions[2];
        } else if (lowerPrompt.includes('todo') || lowerPrompt.includes('implement')) {
            return completions[3];
        } else if (lowerPrompt.includes('try') || lowerPrompt.includes('error')) {
            return completions[4];
        }
        
        // 默认返回第一个补全
        return completions[0];
    }

    /**
     * 更新配置
     * @param newConfig 新的配置
     */
    updateConfig(newConfig: Partial<AICompletionConfig>): void {
        this.config = { ...this.config, ...newConfig };
        Logger.info('AICompletionService 配置已更新', this.config);
    }
}
