/**
 * 聊天消息类型
 */
export enum MessageType {
    USER = 'user',
    ASSISTANT = 'assistant',
    SYSTEM = 'system',
    ERROR = 'error'
}

/**
 * 聊天消息接口
 */
export interface ChatMessage {
    /** 消息唯一标识 */
    id: string;
    /** 消息类型 */
    type: MessageType;
    /** 消息内容 */
    content: string;
    /** 创建时间 */
    timestamp: Date;
    /** 是否正在生成中 */
    isGenerating?: boolean;
    /** 元数据 */
    metadata?: {
        /** 模型名称 */
        model?: string;
        /** 生成耗时（毫秒） */
        duration?: number;
        /** 错误信息 */
        error?: string;
    };
}

/**
 * 聊天会话接口
 */
export interface ChatSession {
    /** 会话唯一标识 */
    id: string;
    /** 会话标题 */
    title: string;
    /** 消息列表 */
    messages: ChatMessage[];
    /** 创建时间 */
    createdAt: Date;
    /** 最后更新时间 */
    updatedAt: Date;
    /** 会话配置 */
    config?: ChatConfig;
}

/**
 * 聊天配置接口
 */
export interface ChatConfig {
    /** AI 模型名称 */
    model: string;
    /** 最大上下文长度 */
    maxContextLength: number;
    /** 温度参数 */
    temperature: number;
    /** 系统提示词 */
    systemPrompt?: string;
    /** 是否启用流式响应 */
    streamResponse: boolean;
    /** 请求超时时间（毫秒） */
    timeout: number;
}

/**
 * 聊天历史接口
 */
export interface ChatHistory {
    /** 会话列表 */
    sessions: ChatSession[];
    /** 当前活跃会话ID */
    activeSessionId?: string;
    /** 最大保存会话数 */
    maxSessions: number;
}

/**
 * 聊天服务配置接口
 */
export interface ChatServiceConfig {
    /** 默认聊天配置 */
    defaultChatConfig: ChatConfig;
    /** 历史记录配置 */
    historyConfig: {
        /** 最大会话数 */
        maxSessions: number;
        /** 每个会话最大消息数 */
        maxMessagesPerSession: number;
        /** 是否自动保存 */
        autoSave: boolean;
    };
    /** 存储配置 */
    storageConfig: {
        /** 存储键前缀 */
        keyPrefix: string;
        /** 是否启用持久化 */
        persistent: boolean;
    };
}

/**
 * Webview 消息类型
 */
export enum WebviewMessageType {
    SEND_MESSAGE = 'sendMessage',
    CLEAR_CHAT = 'clearChat',
    EXPORT_CHAT = 'exportChat',
    LOAD_HISTORY = 'loadHistory',
    UPDATE_CONFIG = 'updateConfig',
    READY = 'ready'
}

/**
 * Webview 消息接口
 */
export interface WebviewMessage {
    /** 消息类型 */
    type: WebviewMessageType;
    /** 消息数据 */
    data?: any;
    /** 请求ID（用于响应匹配） */
    requestId?: string;
}

/**
 * 聊天统计信息接口
 */
export interface ChatStats {
    /** 总会话数 */
    totalSessions: number;
    /** 总消息数 */
    totalMessages: number;
    /** 今日消息数 */
    todayMessages: number;
    /** 平均响应时间（毫秒） */
    averageResponseTime: number;
    /** 最常用的功能 */
    mostUsedFeatures: string[];
}
