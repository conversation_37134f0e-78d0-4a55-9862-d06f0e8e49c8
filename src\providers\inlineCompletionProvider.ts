import * as vscode from 'vscode';
import { AICompletionService } from '../services/aiCompletionService';
import { Logger } from '../utils/logger';

/**
 * 内联补全提供器配置
 */
export interface InlineCompletionConfig {
    /** 触发补全的最小文本长度 */
    minTextLength: number;
    /** 是否启用补全 */
    enabled: boolean;
    /** 支持的文件类型 */
    supportedLanguages: string[];
}

/**
 * 内联补全提供器
 * 实现 VSCode 的 InlineCompletionItemProvider 接口
 */
export class InlineCompletionProvider implements vscode.InlineCompletionItemProvider {
    private aiService: AICompletionService;
    private config: InlineCompletionConfig;

    constructor(aiService: AICompletionService, config?: Partial<InlineCompletionConfig>) {
        this.aiService = aiService;
        this.config = {
            minTextLength: 3,
            enabled: true,
            supportedLanguages: ['typescript', 'javascript', 'python', 'java', 'csharp', 'cpp'],
            ...config
        };
        
        Logger.info('InlineCompletionProvider 初始化完成', this.config);
    }

    /**
     * 提供内联补全项
     * @param document 文档
     * @param position 光标位置
     * @param context 补全上下文
     * @param token 取消令牌
     * @returns 补全项数组
     */
    async provideInlineCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext,
        token: vscode.CancellationToken
    ): Promise<vscode.InlineCompletionItem[] | undefined> {
        
        Logger.methodCall('InlineCompletionProvider', 'provideInlineCompletionItems');
        Logger.debug(`文档: ${document.fileName}, 位置: ${position.line}:${position.character}`);

        // 检查是否启用
        if (!this.config.enabled) {
            Logger.debug('内联补全已禁用');
            return undefined;
        }

        // 检查是否支持当前语言
        if (!this.isSupportedLanguage(document.languageId)) {
            Logger.debug(`不支持的语言: ${document.languageId}`);
            return undefined;
        }

        // 获取光标前的文本
        const textBeforeCursor = this.getTextBeforeCursor(document, position);
        Logger.debug(`光标前文本长度: ${textBeforeCursor.length}`);

        // 检查文本长度
        if (textBeforeCursor.trim().length < this.config.minTextLength) {
            Logger.debug(`文本太短，跳过补全 (最小长度: ${this.config.minTextLength})`);
            return undefined;
        }

        // 检查取消状态
        if (token.isCancellationRequested) {
            Logger.debug('请求已被取消');
            return undefined;
        }

        try {
            // 调用 AI 服务获取补全
            const response = await this.aiService.getCompletion(textBeforeCursor);
            
            // 再次检查取消状态
            if (token.isCancellationRequested) {
                Logger.debug('请求在 AI 调用后被取消');
                return undefined;
            }

            if (!response.success) {
                Logger.warn('AI 补全失败', response.error);
                return undefined;
            }

            // 创建补全项
            const completionItem = this.createCompletionItem(response.completion, position);
            Logger.debug(`返回补全项: ${response.completion}`);
            
            return [completionItem];

        } catch (error) {
            Logger.error('提供内联补全时发生错误', error as Error);
            return undefined;
        }
    }

    /**
     * 检查是否支持当前语言
     * @param languageId 语言 ID
     * @returns 是否支持
     */
    private isSupportedLanguage(languageId: string): boolean {
        return this.config.supportedLanguages.includes(languageId) || 
               this.config.supportedLanguages.includes('*');
    }

    /**
     * 获取光标前的文本
     * @param document 文档
     * @param position 位置
     * @returns 光标前的文本
     */
    private getTextBeforeCursor(document: vscode.TextDocument, position: vscode.Position): string {
        const range = new vscode.Range(new vscode.Position(0, 0), position);
        return document.getText(range);
    }

    /**
     * 创建补全项
     * @param completion 补全文本
     * @param position 插入位置
     * @returns 内联补全项
     */
    private createCompletionItem(completion: string, position: vscode.Position): vscode.InlineCompletionItem {
        // 不指定范围，让 VSCode 自动处理插入位置
        return new vscode.InlineCompletionItem(completion);
    }

    /**
     * 更新配置
     * @param newConfig 新配置
     */
    updateConfig(newConfig: Partial<InlineCompletionConfig>): void {
        this.config = { ...this.config, ...newConfig };
        Logger.info('InlineCompletionProvider 配置已更新', this.config);
    }

    /**
     * 启用/禁用补全
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void {
        this.config.enabled = enabled;
        Logger.info(`内联补全已${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 获取当前配置
     * @returns 当前配置
     */
    getConfig(): InlineCompletionConfig {
        return { ...this.config };
    }
}
