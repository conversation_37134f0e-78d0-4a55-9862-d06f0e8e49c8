{"name": "ai-extension-demo", "displayName": "ai-extension-demo", "description": "ai ide 插件 demo", "version": "0.0.1", "engines": {"vscode": "^1.102.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "ai-chat", "title": "AI Chat", "icon": "$(comment-discussion)"}]}, "views": {"ai-chat": [{"type": "webview", "id": "ai-chat-view", "name": "Cha<PERSON>", "when": "true", "icon": "$(comment-discussion)"}]}, "commands": [{"command": "ai-chat.newChat", "title": "新建聊天", "icon": "$(add)"}, {"command": "ai-chat.clearChat", "title": "清空聊天", "icon": "$(clear-all)"}, {"command": "ai-chat.exportChat", "title": "导出聊天", "icon": "$(export)"}, {"command": "ai-chat.showHistory", "title": "聊天历史", "icon": "$(history)"}, {"command": "ai-chat.settings", "title": "设置", "icon": "$(settings-gear)"}], "menus": {"view/title": [{"command": "ai-chat.newChat", "when": "view == ai-chat-view", "group": "navigation@1"}, {"command": "ai-chat.showHistory", "when": "view == ai-chat-view", "group": "navigation@2"}, {"command": "ai-chat.clearChat", "when": "view == ai-chat-view", "group": "navigation@3"}, {"command": "ai-chat.exportChat", "when": "view == ai-chat-view", "group": "navigation@4"}, {"command": "ai-chat.settings", "when": "view == ai-chat-view", "group": "navigation@5"}]}}, "scripts": {"vscode:prepublish": "yarn run package", "compile": "yarn run check-types && yarn run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "yarn run check-types && yarn run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "yarn run compile-tests && yarn run compile && yarn run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.102.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "esbuild": "^0.25.3", "npm-run-all": "^4.1.5", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2"}}