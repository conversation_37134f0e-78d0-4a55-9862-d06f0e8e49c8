import * as vscode from 'vscode';
import { ChatService } from '../services/chatService';
import { Logger } from '../utils/logger';

/**
 * 聊天命令处理器
 * 负责处理所有与聊天相关的 VSCode 命令
 */
export class ChatCommands {
    private chatService: ChatService;

    constructor(chatService: ChatService) {
        this.chatService = chatService;
        Logger.info('ChatCommands 初始化完成');
    }

    /**
     * 注册所有聊天命令
     * @param context 扩展上下文
     */
    registerCommands(context: vscode.ExtensionContext): void {
        Logger.methodCall('ChatCommands', 'registerCommands');

        const commands = [
            vscode.commands.registerCommand('ai-chat.newChat', this.newChat.bind(this)),
            vscode.commands.registerCommand('ai-chat.clearChat', this.clearChat.bind(this)),
            vscode.commands.registerCommand('ai-chat.exportChat', this.exportChat.bind(this)),
            vscode.commands.registerCommand('ai-chat.showHistory', this.showHistory.bind(this)),
            vscode.commands.registerCommand('ai-chat.settings', this.showSettings.bind(this))
        ];

        commands.forEach(command => context.subscriptions.push(command));
        Logger.success(`已注册 ${commands.length} 个聊天命令`);
    }

    /**
     * 新建聊天会话
     */
    private async newChat(): Promise<void> {
        Logger.methodCall('ChatCommands', 'newChat');

        try {
            const title = await vscode.window.showInputBox({
                prompt: '请输入新聊天会话的标题',
                placeHolder: '例如：代码审查、问题讨论等',
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return '标题不能为空';
                    }
                    if (value.length > 50) {
                        return '标题长度不能超过50个字符';
                    }
                    return null;
                }
            });

            if (title) {
                const session = this.chatService.createNewSession(title.trim());
                vscode.window.showInformationMessage(`新聊天会话 "${session.title}" 已创建`);
                Logger.success(`新聊天会话已创建: ${session.title}`);
            }
        } catch (error) {
            Logger.error('创建新聊天会话失败', error as Error);
            vscode.window.showErrorMessage('创建新聊天会话失败');
        }
    }

    /**
     * 清空当前聊天
     */
    private async clearChat(): Promise<void> {
        Logger.methodCall('ChatCommands', 'clearChat');

        try {
            const confirm = await vscode.window.showWarningMessage(
                '确定要清空当前聊天记录吗？此操作不可撤销。',
                { modal: true },
                '确定清空',
                '取消'
            );

            if (confirm === '确定清空') {
                this.chatService.clearCurrentSession();
                vscode.window.showInformationMessage('聊天记录已清空');
                Logger.success('聊天记录已清空');
            }
        } catch (error) {
            Logger.error('清空聊天记录失败', error as Error);
            vscode.window.showErrorMessage('清空聊天记录失败');
        }
    }

    /**
     * 导出聊天记录
     */
    private async exportChat(): Promise<void> {
        Logger.methodCall('ChatCommands', 'exportChat');

        try {
            const history = this.chatService.getHistory();
            
            if (history.sessions.length === 0) {
                vscode.window.showInformationMessage('没有聊天记录可以导出');
                return;
            }

            // 如果有多个会话，让用户选择
            let sessionToExport = history.sessions[0];
            
            if (history.sessions.length > 1) {
                const sessionItems = history.sessions.map(session => ({
                    label: session.title,
                    description: `${session.messages.length} 条消息 • ${session.updatedAt.toLocaleDateString()}`,
                    session: session
                }));

                const selected = await vscode.window.showQuickPick(sessionItems, {
                    placeHolder: '选择要导出的聊天会话',
                    matchOnDescription: true
                });

                if (!selected) {
                    return;
                }

                sessionToExport = selected.session;
            }

            // 导出聊天记录
            const exportContent = this.chatService.exportChat(sessionToExport.id);
            
            // 让用户选择保存位置
            const saveUri = await vscode.window.showSaveDialog({
                defaultUri: vscode.Uri.file(`${sessionToExport.title}.md`),
                filters: {
                    'Markdown': ['md'],
                    'Text': ['txt'],
                    'All Files': ['*']
                }
            });

            if (saveUri) {
                await vscode.workspace.fs.writeFile(saveUri, Buffer.from(exportContent, 'utf8'));
                vscode.window.showInformationMessage(`聊天记录已导出到: ${saveUri.fsPath}`);
                Logger.success(`聊天记录已导出: ${saveUri.fsPath}`);

                // 询问是否打开导出的文件
                const openFile = await vscode.window.showInformationMessage(
                    '导出成功！是否打开导出的文件？',
                    '打开文件',
                    '稍后查看'
                );

                if (openFile === '打开文件') {
                    await vscode.window.showTextDocument(saveUri);
                }
            }
        } catch (error) {
            Logger.error('导出聊天记录失败', error as Error);
            vscode.window.showErrorMessage('导出聊天记录失败');
        }
    }

    /**
     * 显示聊天历史
     */
    private async showHistory(): Promise<void> {
        Logger.methodCall('ChatCommands', 'showHistory');

        try {
            const history = this.chatService.getHistory();
            
            if (history.sessions.length === 0) {
                vscode.window.showInformationMessage('暂无聊天历史记录');
                return;
            }

            const sessionItems = history.sessions.map(session => ({
                label: session.title,
                description: `${session.messages.length} 条消息`,
                detail: `创建时间: ${session.createdAt.toLocaleString()} • 最后更新: ${session.updatedAt.toLocaleString()}`,
                session: session
            }));

            const selected = await vscode.window.showQuickPick(sessionItems, {
                placeHolder: '选择聊天会话查看详情',
                matchOnDescription: true,
                matchOnDetail: true
            });

            if (selected) {
                // 显示会话详情
                await this.showSessionDetails(selected.session);
            }
        } catch (error) {
            Logger.error('显示聊天历史失败', error as Error);
            vscode.window.showErrorMessage('显示聊天历史失败');
        }
    }

    /**
     * 显示设置
     */
    private async showSettings(): Promise<void> {
        Logger.methodCall('ChatCommands', 'showSettings');

        try {
            const stats = this.chatService.getStats();
            
            const settingItems = [
                {
                    label: '📊 查看统计信息',
                    description: `${stats.totalSessions} 个会话 • ${stats.totalMessages} 条消息`,
                    action: 'stats'
                },
                {
                    label: '🗑️ 清理历史记录',
                    description: '删除所有聊天历史记录',
                    action: 'cleanup'
                },
                {
                    label: '⚙️ 打开设置',
                    description: '打开扩展设置页面',
                    action: 'settings'
                },
                {
                    label: '📖 查看帮助',
                    description: '查看使用说明和快捷键',
                    action: 'help'
                }
            ];

            const selected = await vscode.window.showQuickPick(settingItems, {
                placeHolder: '选择操作',
                matchOnDescription: true
            });

            if (selected) {
                await this.handleSettingAction(selected.action, stats);
            }
        } catch (error) {
            Logger.error('显示设置失败', error as Error);
            vscode.window.showErrorMessage('显示设置失败');
        }
    }

    /**
     * 显示会话详情
     * @param session 会话对象
     */
    private async showSessionDetails(session: any): Promise<void> {
        const actions = [
            {
                label: '💬 切换到此会话',
                description: '将此会话设为当前活跃会话',
                action: 'switch'
            },
            {
                label: '📤 导出此会话',
                description: '将此会话导出为文件',
                action: 'export'
            },
            {
                label: '🗑️ 删除此会话',
                description: '永久删除此会话（不可撤销）',
                action: 'delete'
            }
        ];

        const selected = await vscode.window.showQuickPick(actions, {
            placeHolder: `会话: ${session.title}`,
            matchOnDescription: true
        });

        if (selected) {
            switch (selected.action) {
                case 'switch':
                    // 这里需要与 ChatViewProvider 通信来切换会话
                    vscode.window.showInformationMessage(`已切换到会话: ${session.title}`);
                    break;
                case 'export':
                    await this.exportSpecificSession(session.id);
                    break;
                case 'delete':
                    await this.deleteSession(session);
                    break;
            }
        }
    }

    /**
     * 处理设置操作
     * @param action 操作类型
     * @param stats 统计信息
     */
    private async handleSettingAction(action: string, stats: any): Promise<void> {
        switch (action) {
            case 'stats':
                await this.showStats(stats);
                break;
            case 'cleanup':
                await this.cleanupHistory();
                break;
            case 'settings':
                await vscode.commands.executeCommand('workbench.action.openSettings', 'ai-extension-demo');
                break;
            case 'help':
                await this.showHelp();
                break;
        }
    }

    /**
     * 显示统计信息
     * @param stats 统计信息
     */
    private async showStats(stats: any): Promise<void> {
        const message = `
📊 聊天统计信息

• 总会话数: ${stats.totalSessions}
• 总消息数: ${stats.totalMessages}
• 今日消息数: ${stats.todayMessages}
• 平均响应时间: ${stats.averageResponseTime}ms
• 常用功能: ${stats.mostUsedFeatures.join(', ')}
        `.trim();

        vscode.window.showInformationMessage(message, { modal: true });
    }

    /**
     * 清理历史记录
     */
    private async cleanupHistory(): Promise<void> {
        const confirm = await vscode.window.showWarningMessage(
            '确定要删除所有聊天历史记录吗？此操作不可撤销。',
            { modal: true },
            '确定删除',
            '取消'
        );

        if (confirm === '确定删除') {
            // 这里需要在 ChatService 中添加清理方法
            vscode.window.showInformationMessage('所有聊天历史记录已清理');
            Logger.success('聊天历史记录已清理');
        }
    }

    /**
     * 显示帮助信息
     */
    private async showHelp(): Promise<void> {
        const helpMessage = `
🤖 AI 助手使用说明

快捷键:
• Ctrl+Enter: 发送消息
• Shift+Enter: 换行

命令:
• 新建聊天: 创建新的聊天会话
• 清空聊天: 清空当前会话的所有消息
• 导出聊天: 将聊天记录导出为文件
• 聊天历史: 查看和管理历史会话
• 设置: 查看统计信息和配置选项

提示:
• 可以直接粘贴代码让 AI 帮助分析
• 支持多轮对话，AI 会记住上下文
• 消息限制 2000 字符
        `.trim();

        vscode.window.showInformationMessage(helpMessage, { modal: true });
    }

    /**
     * 导出特定会话
     * @param sessionId 会话ID
     */
    private async exportSpecificSession(sessionId: string): Promise<void> {
        try {
            const exportContent = this.chatService.exportChat(sessionId);
            
            const saveUri = await vscode.window.showSaveDialog({
                defaultUri: vscode.Uri.file(`chat-export-${Date.now()}.md`),
                filters: {
                    'Markdown': ['md'],
                    'Text': ['txt']
                }
            });

            if (saveUri) {
                await vscode.workspace.fs.writeFile(saveUri, Buffer.from(exportContent, 'utf8'));
                vscode.window.showInformationMessage(`聊天记录已导出到: ${saveUri.fsPath}`);
            }
        } catch (error) {
            Logger.error('导出会话失败', error as Error);
            vscode.window.showErrorMessage('导出会话失败');
        }
    }

    /**
     * 删除会话
     * @param session 会话对象
     */
    private async deleteSession(session: any): Promise<void> {
        const confirm = await vscode.window.showWarningMessage(
            `确定要删除会话 "${session.title}" 吗？此操作不可撤销。`,
            { modal: true },
            '确定删除',
            '取消'
        );

        if (confirm === '确定删除') {
            this.chatService.deleteSession(session.id);
            vscode.window.showInformationMessage(`会话 "${session.title}" 已删除`);
            Logger.success(`会话已删除: ${session.title}`);
        }
    }
}
