import { createApp, ref, onMounted } from 'vue';

// 声明VSCode API
declare global {
    interface Window {
        acquireVsCodeApi(): any;
    }
}

// 定义App组件
const App = {
  setup() {
    // 响应式数据
    const messageInput = ref('');
    const isGenerating = ref(false);
    const messages = ref<any[]>([]);
    const showWelcome = ref(true);

    // VSCode API
    let vscode: any;

    // 组件挂载时初始化
    onMounted(() => {
      vscode = (window as any).acquireVsCodeApi();

      // 监听来自扩展的消息
      window.addEventListener('message', handleMessage);

      // 通知VSCode扩展webview已准备就绪
      vscode.postMessage({ type: 'ready' });
    });

    // 添加消息到界面
    const addMessage = (type: string, content: string, timestamp?: string) => {
      showWelcome.value = false;
      messages.value.push({
        id: Date.now().toString(),
        type,
        content,
        timestamp: timestamp || new Date().toLocaleTimeString()
      });

      // 滚动到底部
      setTimeout(() => {
        const container = document.getElementById('messages-container');
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      }, 100);
    };

    // 清空消息
    const clearMessages = () => {
      messages.value = [];
      showWelcome.value = true;
    };

    // 发送消息
    const sendMessage = () => {
      const content = messageInput.value.trim();
      if (!content || isGenerating.value) {
        return;
      }

      vscode.postMessage({
        type: 'sendMessage',
        data: { content }
      });

      // 添加用户消息到界面
      addMessage('user', content);
      messageInput.value = '';
      isGenerating.value = true;
    };

    // 快速发送预设消息
    const sendQuickMessage = (content: string) => {
      messageInput.value = content;
      sendMessage();
    };

    // 处理键盘事件
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        if (e.ctrlKey) {
          e.preventDefault();
          sendMessage();
        }
        // Shift+Enter 允许换行，不做处理
      }
    };

    // 处理来自扩展的消息
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;

      switch (message.type) {
        case 'addMessage':
          isGenerating.value = false;
          addMessage(message.data.type, message.data.content, message.data.timestamp);
          break;
        case 'clearMessages':
          clearMessages();
          break;
        case 'error':
          isGenerating.value = false;
          addMessage('error', message.data.content);
          break;
      }
    };

    return {
      messageInput,
      isGenerating,
      messages,
      showWelcome,
      sendMessage,
      sendQuickMessage,
      handleKeyDown,
      addMessage,
      clearMessages
    };
  },
  template: `
    <div class="chat-container">
      <!-- 聊天消息区域 -->
      <div class="messages-container" id="messages-container">
        <!-- 欢迎消息 -->
        <div v-if="showWelcome" class="welcome-message">
          <div class="welcome-icon">🤖</div>
          <h3>欢迎使用 AI 助手</h3>
          <p>我是您的编程助手，可以帮助您解决代码问题、提供技术建议和进行代码审查。</p>
          <div class="quick-actions">
            <button class="quick-btn" @click="sendQuickMessage('帮我解释这段代码')">解释代码</button>
            <button class="quick-btn" @click="sendQuickMessage('帮我优化这段代码')">优化代码</button>
            <button class="quick-btn" @click="sendQuickMessage('帮我找出代码中的问题')">查找问题</button>
          </div>
        </div>

        <!-- 消息列表 -->
        <div v-for="message in messages" :key="message.id" :class="['message', message.type]">
          <div class="message-avatar">
            {{ message.type === 'user' ? '👤' : message.type === 'assistant' ? '🤖' : '❌' }}
          </div>
          <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
            <div class="message-time">{{ message.timestamp }}</div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-container">
        <div class="input-wrapper">
          <textarea
            id="message-input"
            v-model="messageInput"
            placeholder="输入您的问题..."
            rows="3"
            maxlength="2000"
            @keydown="handleKeyDown"
          ></textarea>
          <div class="input-actions">
            <button
              id="send-button"
              class="send-btn"
              title="发送消息 (Ctrl+Enter)"
              @click="sendMessage"
              :disabled="isGenerating"
            >
              <span class="send-icon">📤</span>
            </button>
          </div>
        </div>
        <div class="input-footer">
          <span class="char-count" id="char-count">{{ messageInput.length }}/2000</span>
          <span class="shortcuts">Ctrl+Enter 发送 • Shift+Enter 换行</span>
        </div>
      </div>

      <!-- 加载指示器 -->
      <div class="loading-indicator" v-show="isGenerating">
        <div class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span>AI 正在思考中...</span>
      </div>
    </div>
  `
};

// 创建Vue应用
const app = createApp(App);

// 挂载应用
app.mount('#app');
