import * as vscode from 'vscode';

/**
 * 聊天 Webview HTML 模板生成器
 */
export class ChatWebviewTemplate {
    
    /**
     * 生成聊天界面的 HTML
     * @param webview Webview 实例
     * @returns HTML 字符串
     */
    static getHtml(webview: vscode.Webview): string {
        // 获取样式和脚本的 URI（如果有工作区的话）
        let styleUri: vscode.Uri | undefined;

        if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
            styleUri = webview.asWebviewUri(vscode.Uri.joinPath(
                vscode.workspace.workspaceFolders[0].uri, 'media', 'chat.css'
            ));
        }

        return `
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src ${webview.cspSource} 'unsafe-inline';">
                <title>AI 助手聊天</title>
                <style>
                    ${this.getStyles()}
                </style>
            </head>
            <body>
                <div class="chat-container">
                    <!-- 聊天消息区域 -->
                    <div class="messages-container" id="messages-container">
                        <div class="welcome-message">
                            <div class="welcome-icon">🤖</div>
                            <h3>欢迎使用 AI 助手</h3>
                            <p>我是您的编程助手，可以帮助您解决代码问题、提供技术建议和进行代码审查。</p>
                            <div class="quick-actions">
                                <button class="quick-btn" onclick="sendQuickMessage('帮我解释这段代码')">解释代码</button>
                                <button class="quick-btn" onclick="sendQuickMessage('帮我优化这段代码')">优化代码</button>
                                <button class="quick-btn" onclick="sendQuickMessage('帮我找出代码中的问题')">查找问题</button>
                            </div>
                        </div>
                    </div>

                    <!-- 输入区域 -->
                    <div class="input-container">
                        <div class="input-wrapper">
                            <textarea 
                                id="message-input" 
                                placeholder="输入您的问题..." 
                                rows="3"
                                maxlength="2000"
                            ></textarea>
                            <div class="input-actions">
                                <button id="send-button" class="send-btn" title="发送消息 (Ctrl+Enter)">
                                    <span class="send-icon">📤</span>
                                </button>
                            </div>
                        </div>
                        <div class="input-footer">
                            <span class="char-count" id="char-count">0/2000</span>
                            <span class="shortcuts">Ctrl+Enter 发送 • Shift+Enter 换行</span>
                        </div>
                    </div>
                </div>

                <!-- 加载指示器 -->
                <div class="loading-indicator" id="loading-indicator" style="display: none;">
                    <div class="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span>AI 正在思考中...</span>
                </div>

                <script>
                    ${this.getScript()}
                </script>
            </body>
            </html>
        `;
    }

    /**
     * 获取 CSS 样式
     * @returns CSS 字符串
     */
    private static getStyles(): string {
        return `
            * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background-color: var(--vscode-editor-background);
                color: var(--vscode-editor-foreground);
                height: 100vh;
                overflow: hidden;
            }

            .chat-container {
                display: flex;
                flex-direction: column;
                height: 100vh;
            }

            .messages-container {
                flex: 1;
                overflow-y: auto;
                padding: 16px;
                scroll-behavior: smooth;
            }

            .welcome-message {
                text-align: center;
                padding: 40px 20px;
                opacity: 0.8;
            }

            .welcome-icon {
                font-size: 48px;
                margin-bottom: 16px;
            }

            .welcome-message h3 {
                color: var(--vscode-textLink-foreground);
                margin-bottom: 8px;
            }

            .welcome-message p {
                color: var(--vscode-descriptionForeground);
                line-height: 1.5;
                margin-bottom: 24px;
            }

            .quick-actions {
                display: flex;
                gap: 8px;
                justify-content: center;
                flex-wrap: wrap;
            }

            .quick-btn {
                background: var(--vscode-button-secondaryBackground);
                color: var(--vscode-button-secondaryForeground);
                border: 1px solid var(--vscode-button-border);
                border-radius: 16px;
                padding: 6px 12px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s;
            }

            .quick-btn:hover {
                background: var(--vscode-button-secondaryHoverBackground);
                transform: translateY(-1px);
            }

            .message {
                margin-bottom: 16px;
                display: flex;
                align-items: flex-start;
                gap: 12px;
            }

            .message.user {
                flex-direction: row-reverse;
            }

            .message-avatar {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                flex-shrink: 0;
            }

            .message.user .message-avatar {
                background: var(--vscode-textLink-foreground);
                color: white;
            }

            .message.assistant .message-avatar {
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
            }

            .message.error .message-avatar {
                background: var(--vscode-errorForeground);
                color: white;
            }

            .message-content {
                flex: 1;
                background: var(--vscode-input-background);
                border: 1px solid var(--vscode-input-border);
                border-radius: 12px;
                padding: 12px 16px;
                max-width: 80%;
            }

            .message.user .message-content {
                background: var(--vscode-textLink-foreground);
                color: white;
                border-color: var(--vscode-textLink-foreground);
            }

            .message-text {
                line-height: 1.5;
                white-space: pre-wrap;
                word-wrap: break-word;
            }

            .message-time {
                font-size: 11px;
                color: var(--vscode-descriptionForeground);
                margin-top: 4px;
            }

            .input-container {
                border-top: 1px solid var(--vscode-panel-border);
                background: var(--vscode-panel-background);
                padding: 16px;
            }

            .input-wrapper {
                display: flex;
                gap: 8px;
                align-items: flex-end;
            }

            #message-input {
                flex: 1;
                background: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                border: 1px solid var(--vscode-input-border);
                border-radius: 8px;
                padding: 12px;
                font-family: inherit;
                font-size: 14px;
                resize: none;
                outline: none;
                transition: border-color 0.2s;
            }

            #message-input:focus {
                border-color: var(--vscode-focusBorder);
            }

            .send-btn {
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                border-radius: 8px;
                width: 40px;
                height: 40px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s;
            }

            .send-btn:hover {
                background: var(--vscode-button-hoverBackground);
                transform: scale(1.05);
            }

            .send-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none;
            }

            .input-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 8px;
                font-size: 11px;
                color: var(--vscode-descriptionForeground);
            }

            .loading-indicator {
                position: fixed;
                bottom: 100px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--vscode-notifications-background);
                border: 1px solid var(--vscode-notifications-border);
                border-radius: 8px;
                padding: 12px 16px;
                display: flex;
                align-items: center;
                gap: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .loading-dots {
                display: flex;
                gap: 4px;
            }

            .loading-dots span {
                width: 6px;
                height: 6px;
                background: var(--vscode-textLink-foreground);
                border-radius: 50%;
                animation: loading 1.4s infinite ease-in-out;
            }

            .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
            .loading-dots span:nth-child(2) { animation-delay: -0.16s; }

            @keyframes loading {
                0%, 80%, 100% { transform: scale(0); }
                40% { transform: scale(1); }
            }

            /* 滚动条样式 */
            .messages-container::-webkit-scrollbar {
                width: 6px;
            }

            .messages-container::-webkit-scrollbar-track {
                background: var(--vscode-scrollbarSlider-background);
            }

            .messages-container::-webkit-scrollbar-thumb {
                background: var(--vscode-scrollbarSlider-hoverBackground);
                border-radius: 3px;
            }
        `;
    }

    /**
     * 获取 JavaScript 脚本
     * @returns JavaScript 字符串
     */
    private static getScript(): string {
        return `
            const vscode = acquireVsCodeApi();
            const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');
            const messagesContainer = document.getElementById('messages-container');
            const charCount = document.getElementById('char-count');
            const loadingIndicator = document.getElementById('loading-indicator');

            // 初始化
            let isGenerating = false;

            // 发送消息到扩展
            function sendMessage(content) {
                if (!content.trim() || isGenerating) return;

                vscode.postMessage({
                    type: 'sendMessage',
                    data: { content: content.trim() }
                });

                addMessage('user', content.trim());
                messageInput.value = '';
                updateCharCount();
                showLoading();
                setGenerating(true);
            }

            // 快速发送预设消息
            function sendQuickMessage(content) {
                messageInput.value = content;
                sendMessage(content);
            }

            // 添加消息到界面
            function addMessage(type, content, timestamp) {
                // 移除欢迎消息
                const welcomeMessage = document.querySelector('.welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.remove();
                }

                const messageDiv = document.createElement('div');
                messageDiv.className = \`message \${type}\`;

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = type === 'user' ? '👤' : type === 'assistant' ? '🤖' : '❌';

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';

                const textDiv = document.createElement('div');
                textDiv.className = 'message-text';
                textDiv.textContent = content;

                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                timeDiv.textContent = timestamp || new Date().toLocaleTimeString();

                contentDiv.appendChild(textDiv);
                contentDiv.appendChild(timeDiv);
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(contentDiv);

                messagesContainer.appendChild(messageDiv);
                scrollToBottom();
            }

            // 显示加载指示器
            function showLoading() {
                loadingIndicator.style.display = 'flex';
            }

            // 隐藏加载指示器
            function hideLoading() {
                loadingIndicator.style.display = 'none';
            }

            // 设置生成状态
            function setGenerating(generating) {
                isGenerating = generating;
                sendButton.disabled = generating;
                messageInput.disabled = generating;
            }

            // 滚动到底部
            function scrollToBottom() {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            // 更新字符计数
            function updateCharCount() {
                const count = messageInput.value.length;
                charCount.textContent = \`\${count}/2000\`;
                charCount.style.color = count > 1800 ? 'var(--vscode-errorForeground)' : 'var(--vscode-descriptionForeground)';
            }

            // 事件监听器
            sendButton.addEventListener('click', () => {
                sendMessage(messageInput.value);
            });

            messageInput.addEventListener('input', updateCharCount);

            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    if (e.ctrlKey) {
                        e.preventDefault();
                        sendMessage(messageInput.value);
                    } else if (!e.shiftKey) {
                        // 允许 Shift+Enter 换行
                        // 其他情况下阻止默认行为（可选）
                    }
                }
            });

            // 监听来自扩展的消息
            window.addEventListener('message', event => {
                const message = event.data;
                
                switch (message.type) {
                    case 'addMessage':
                        hideLoading();
                        setGenerating(false);
                        addMessage(message.data.type, message.data.content, message.data.timestamp);
                        break;
                    case 'clearMessages':
                        messagesContainer.innerHTML = '';
                        hideLoading();
                        setGenerating(false);
                        break;
                    case 'error':
                        hideLoading();
                        setGenerating(false);
                        addMessage('error', message.data.content);
                        break;
                }
            });

            // 通知扩展 webview 已准备就绪
            vscode.postMessage({ type: 'ready' });
        `;
    }
}
