// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';
import { AICompletionService } from './services/aiCompletionService';
import { ChatService } from './services/chatService';
import { InlineCompletionProvider } from './providers/inlineCompletionProvider';
import { ChatViewProvider } from './providers/ChatViewProvider';
import { ChatCommands } from './commands/chatCommands';
import { Logger } from './utils/logger';

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	Logger.info('扩展 "ai-extension-demo" 已激活!');

	// The command has been defined in the package.json file
	// Now provide the implementation of the command with registerCommand
	// The commandId parameter must match the command field in package.json
	const disposable = vscode.commands.registerCommand('ai-extension-demo.helloWorld', () => {
		// The code you place here will be executed every time your command is executed
		// Display a message box to the user
		vscode.window.showInformationMessage('Hello World from ai-extension-demo!');
	});

	context.subscriptions.push(disposable);

	// 初始化服务
	Logger.info('正在初始化 AI 补全服务...');
	const aiService = new AICompletionService({
		maxPromptLength: 1000,
		timeout: 5000,
		simulatedDelay: 50
	});

	Logger.info('正在初始化聊天服务...');
	const chatService = new ChatService(aiService, context, {
		defaultChatConfig: {
			model: 'gpt-3.5-turbo',
			maxContextLength: 4000,
			temperature: 0.7,
			systemPrompt: '你是一个有用的AI助手，专门帮助开发者解决编程问题。',
			streamResponse: false,
			timeout: 30000
		},
		historyConfig: {
			maxSessions: 50,
			maxMessagesPerSession: 100,
			autoSave: true
		}
	});

	// 初始化提供器
	Logger.info('正在初始化内联补全提供器...');
	const inlineProvider = new InlineCompletionProvider(aiService, {
		minTextLength: 3,
		enabled: true,
		supportedLanguages: ['*'] // 支持所有语言
	});

	Logger.info('正在初始化聊天视图提供器...');
	const chatViewProvider = new ChatViewProvider(chatService);

	// 初始化命令处理器
	Logger.info('正在初始化聊天命令处理器...');
	const chatCommands = new ChatCommands(chatService);

	// 注册所有组件
	Logger.info('正在注册内联补全提供器...');
    const inlineCompletionItemDisposable = vscode.languages.registerInlineCompletionItemProvider(
        { scheme: 'file' }, // 使用正确的文档选择器
        inlineProvider
    );
    context.subscriptions.push(inlineCompletionItemDisposable);
    Logger.success('内联补全提供器注册完成');

	Logger.info('正在注册聊天视图提供器...');
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(ChatViewProvider.viewType, chatViewProvider)
    );
	Logger.success('聊天视图提供器注册完成');

	Logger.info('正在注册聊天命令...');
	chatCommands.registerCommands(context);
	Logger.success('聊天命令注册完成');
}

// This method is called when your extension is deactivated
export function deactivate() {}
