<template>
  <div class="chat-container">
    <!-- 聊天消息区域 -->
    <div class="messages-container" id="messages-container">
      <div class="welcome-message">
        <div class="welcome-icon">🤖</div>
        <h3>欢迎使用 AI 助手</h3>
        <p>我是您的编程助手，可以帮助您解决代码问题、提供技术建议和进行代码审查。</p>
        <div class="quick-actions">
          <button class="quick-btn" @click="sendQuickMessage('帮我解释这段代码')">解释代码</button>
          <button class="quick-btn" @click="sendQuickMessage('帮我优化这段代码')">优化代码</button>
          <button class="quick-btn" @click="sendQuickMessage('帮我找出代码中的问题')">查找问题</button>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
      <div class="input-wrapper">
        <textarea 
          id="message-input" 
          v-model="messageInput"
          placeholder="输入您的问题..." 
          rows="3"
          maxlength="2000"
          @keydown="handleKeyDown"
        ></textarea>
        <div class="input-actions">
          <button 
            id="send-button" 
            class="send-btn" 
            title="发送消息 (Ctrl+Enter)"
            @click="sendMessage"
            :disabled="isGenerating"
          >
            <span class="send-icon">📤</span>
          </button>
        </div>
      </div>
      <div class="input-footer">
        <span class="char-count" id="char-count">{{ messageInput.length }}/2000</span>
        <span class="shortcuts">Ctrl+Enter 发送 • Shift+Enter 换行</span>
      </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-indicator" v-show="isGenerating">
      <div class="loading-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <span>AI 正在思考中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 响应式数据
const messageInput = ref('');
const isGenerating = ref(false);

// VSCode API
let vscode: any;

// 组件挂载时初始化
onMounted(() => {
  vscode = (window as any).acquireVsCodeApi();
  
  // 监听来自扩展的消息
  window.addEventListener('message', handleMessage);
});

// 发送消息
const sendMessage = () => {
  const content = messageInput.value.trim();
  if (!content || isGenerating.value) return;

  vscode.postMessage({
    type: 'sendMessage',
    data: { content }
  });

  messageInput.value = '';
  isGenerating.value = true;
};

// 快速发送预设消息
const sendQuickMessage = (content: string) => {
  messageInput.value = content;
  sendMessage();
};

// 处理键盘事件
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    if (e.ctrlKey) {
      e.preventDefault();
      sendMessage();
    }
    // Shift+Enter 允许换行，不做处理
  }
};

// 处理来自扩展的消息
const handleMessage = (event: MessageEvent) => {
  const message = event.data;
  
  switch (message.type) {
    case 'addMessage':
      isGenerating.value = false;
      // TODO: 添加消息到界面
      break;
    case 'clearMessages':
      // TODO: 清空消息
      break;
    case 'error':
      isGenerating.value = false;
      // TODO: 显示错误消息
      break;
  }
};
</script>

<style scoped>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  scroll-behavior: smooth;
}

.welcome-message {
  text-align: center;
  padding: 40px 20px;
  opacity: 0.8;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.welcome-message h3 {
  color: var(--vscode-textLink-foreground);
  margin-bottom: 8px;
}

.welcome-message p {
  color: var(--vscode-descriptionForeground);
  line-height: 1.5;
  margin-bottom: 24px;
}

.quick-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.quick-btn {
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border: 1px solid var(--vscode-button-border);
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-btn:hover {
  background: var(--vscode-button-secondaryHoverBackground);
  transform: translateY(-1px);
}

.input-container {
  border-top: 1px solid var(--vscode-panel-border);
  background: var(--vscode-panel-background);
  padding: 16px;
}

.input-wrapper {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

#message-input {
  flex: 1;
  background: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border: 1px solid var(--vscode-input-border);
  border-radius: 8px;
  padding: 12px;
  font-family: inherit;
  font-size: 14px;
  resize: none;
  outline: none;
  transition: border-color 0.2s;
}

#message-input:focus {
  border-color: var(--vscode-focusBorder);
}

.send-btn {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.send-btn:hover {
  background: var(--vscode-button-hoverBackground);
  transform: scale(1.05);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.loading-indicator {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--vscode-notifications-background);
  border: 1px solid var(--vscode-notifications-border);
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background: var(--vscode-textLink-foreground);
  border-radius: 50%;
  animation: loading 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-hoverBackground);
  border-radius: 3px;
}
</style>
